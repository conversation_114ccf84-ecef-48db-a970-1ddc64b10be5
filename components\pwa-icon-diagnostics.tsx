"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import {
  Image,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ExternalLink,
  Download,
  Smartphone,
  Monitor
} from "lucide-react"

interface IconInfo {
  src: string
  sizes: string
  type: string
  purpose: string
  exists: boolean
  error?: string
  category?: "manifest" | "apple-touch" | "maskable"
}

export function PWAIconDiagnostics() {
  const [icons, setIcons] = useState<IconInfo[]>([])
  const [manifestData, setManifestData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [lastCheck, setLastCheck] = useState<Date | null>(null)

  const checkIcons = async () => {
    setIsLoading(true)
    try {
      // Fetch manifest
      const manifestResponse = await fetch('/manifest.json')
      if (!manifestResponse.ok) {
        throw new Error(`Manifest not found: ${manifestResponse.status}`)
      }

      const manifest = await manifestResponse.json()
      setManifestData(manifest)

      // Check manifest icons
      const manifestIconChecks = await Promise.all(
        manifest.icons?.map(async (icon: any) => {
          try {
            const response = await fetch(icon.src)
            return {
              ...icon,
              category: icon.purpose === "maskable" ? "maskable" : "manifest",
              exists: response.ok,
              error: response.ok ? undefined : `HTTP ${response.status}`
            }
          } catch (error) {
            return {
              ...icon,
              category: icon.purpose === "maskable" ? "maskable" : "manifest",
              exists: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          }
        }) || []
      )

      // Check Apple Touch Icons
      const appleTouchIcons = [
        { src: "/apple-touch-icon-180x180.png", sizes: "180x180" },
        { src: "/apple-touch-icon-152x152.png", sizes: "152x152" },
        { src: "/apple-touch-icon-144x144.png", sizes: "144x144" },
        { src: "/apple-touch-icon-120x120.png", sizes: "120x120" },
        { src: "/apple-touch-icon-114x114.png", sizes: "114x114" },
        { src: "/apple-touch-icon-76x76.png", sizes: "76x76" },
        { src: "/apple-touch-icon-72x72.png", sizes: "72x72" },
        { src: "/apple-touch-icon-60x60.png", sizes: "60x60" },
        { src: "/apple-touch-icon-57x57.png", sizes: "57x57" },
      ]

      const appleTouchIconChecks = await Promise.all(
        appleTouchIcons.map(async (icon) => {
          try {
            const response = await fetch(icon.src)
            return {
              ...icon,
              type: "image/png",
              purpose: "apple-touch",
              category: "apple-touch" as const,
              exists: response.ok,
              error: response.ok ? undefined : `HTTP ${response.status}`
            }
          } catch (error) {
            return {
              ...icon,
              type: "image/png",
              purpose: "apple-touch",
              category: "apple-touch" as const,
              exists: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          }
        })
      )

      const allIcons = [...manifestIconChecks, ...appleTouchIconChecks]
      setIcons(allIcons)
      setLastCheck(new Date())
    } catch (error) {
      console.error('Error checking icons:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    checkIcons()
  }, [])

  const getStatusIcon = (exists: boolean, error?: string) => {
    if (exists) return <CheckCircle className="h-4 w-4 text-green-500" />
    return <XCircle className="h-4 w-4 text-red-500" />
  }

  const getStatusBadge = (exists: boolean) => {
    return exists ? (
      <Badge className="bg-green-100 text-green-800">Disponible</Badge>
    ) : (
      <Badge className="bg-red-100 text-red-800">Manquant</Badge>
    )
  }

  const getPurposeBadge = (purpose: string) => {
    const variants = {
      any: "bg-blue-100 text-blue-800",
      maskable: "bg-purple-100 text-purple-800"
    }

    return (
      <Badge className={variants[purpose as keyof typeof variants] || "bg-gray-100 text-gray-800"}>
        {purpose}
      </Badge>
    )
  }

  const manifestIcons = icons.filter(icon => icon.category === "manifest")
  const maskableIcons = icons.filter(icon => icon.category === "maskable")
  const appleTouchIcons = icons.filter(icon => icon.category === "apple-touch")
  const missingMaskableIcons = maskableIcons.filter(icon => !icon.exists)
  const missingAppleTouchIcons = appleTouchIcons.filter(icon => !icon.exists)
  const hasIconIssues = icons.some(icon => !icon.exists) || maskableIcons.length === 0 || missingAppleTouchIcons.length > 0

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Image className="h-5 w-5" />
          Diagnostic des icônes PWA
        </CardTitle>
        <CardDescription>
          Analyse des icônes PWA et détection des problèmes d'affichage
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Contrôles */}
        <div className="flex items-center justify-between">
          <Button onClick={checkIcons} disabled={isLoading}>
            {isLoading ? "Vérification..." : "Vérifier les icônes"}
          </Button>
          {lastCheck && (
            <span className="text-xs text-muted-foreground">
              Dernière vérification: {lastCheck.toLocaleTimeString()}
            </span>
          )}
        </div>

        {/* Alerte principale */}
        {hasIconIssues && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Problème détecté:</strong> Les icônes PWA peuvent apparaître différemment selon la méthode d'installation.
              {missingMaskableIcons.length > 0 && " Des icônes maskable dédiées sont manquantes."}
              {missingAppleTouchIcons.length > 0 && " Des icônes Apple Touch pour iOS sont manquantes."}
              <br />
              <small className="text-muted-foreground">
                Installation native (Chrome/Edge) vs "Ajouter à l'écran d'accueil" (Safari iOS) peuvent utiliser des icônes différentes.
              </small>
            </AlertDescription>
          </Alert>
        )}

        {/* Résumé */}
        {icons.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center space-y-1">
              <div className="text-2xl font-bold text-blue-600">{manifestIcons.length}</div>
              <div className="text-xs text-muted-foreground">Manifest</div>
            </div>
            <div className="text-center space-y-1">
              <div className="text-2xl font-bold text-purple-600">{maskableIcons.length}</div>
              <div className="text-xs text-muted-foreground">Maskable</div>
            </div>
            <div className="text-center space-y-1">
              <div className="text-2xl font-bold text-orange-600">{appleTouchIcons.length}</div>
              <div className="text-xs text-muted-foreground">Apple Touch</div>
            </div>
            <div className="text-center space-y-1">
              <div className="text-2xl font-bold text-green-600">
                {icons.filter(icon => icon.exists).length}
              </div>
              <div className="text-xs text-muted-foreground">Disponibles</div>
            </div>
            <div className="text-center space-y-1">
              <div className="text-2xl font-bold text-red-600">
                {icons.filter(icon => !icon.exists).length}
              </div>
              <div className="text-xs text-muted-foreground">Manquantes</div>
            </div>
          </div>
        )}

        <Separator />

        {/* Liste des icônes */}
        <div className="space-y-4">
          <h4 className="font-semibold">Icônes configurées</h4>
          {icons.length === 0 ? (
            <p className="text-muted-foreground text-center py-4">
              Aucune icône trouvée. Cliquez sur "Vérifier les icônes" pour analyser.
            </p>
          ) : (
            <div className="space-y-3">
              {icons.map((icon, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(icon.exists, icon.error)}
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{icon.src}</span>
                        {getPurposeBadge(icon.purpose)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {icon.sizes} • {icon.type}
                        {icon.error && (
                          <span className="text-red-600 ml-2">• {icon.error}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(icon.exists)}
                    {icon.exists && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(icon.src, '_blank')}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <Separator />

        {/* Explication du problème */}
        <div className="space-y-4">
          <h4 className="font-semibold">Comprendre les méthodes d'installation</h4>
          <div className="bg-muted p-4 rounded-lg space-y-3">
            <div className="flex items-start gap-3">
              <Monitor className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h5 className="font-medium">Installation native PWA (Chrome/Edge)</h5>
                <p className="text-sm text-muted-foreground">
                  Utilise les icônes du manifest.json avec purpose "any". Affichage généralement correct.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Smartphone className="h-5 w-5 text-orange-500 mt-0.5" />
              <div>
                <h5 className="font-medium">"Ajouter à l'écran d'accueil" (Safari iOS)</h5>
                <p className="text-sm text-muted-foreground">
                  Utilise les Apple Touch Icons. Nécessite des icônes spécifiques avec moins de padding.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Monitor className="h-5 w-5 text-purple-500 mt-0.5" />
              <div>
                <h5 className="font-medium">Icônes "maskable" (Android adaptatif)</h5>
                <p className="text-sm text-muted-foreground">
                  Pour les systèmes d'icônes adaptatives. Logo dans une "zone sûre" (80% du canvas).
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions recommandées */}
        <div className="space-y-4">
          <h4 className="font-semibold">Actions recommandées</h4>
          <div className="space-y-2">
            {missingMaskableIcons.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Créer des icônes maskable dédiées:</strong> Utilisez l'outil de génération pour créer des icônes spécifiquement conçues pour les systèmes adaptatifs.
                </AlertDescription>
              </Alert>
            )}

            {missingAppleTouchIcons.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Créer des Apple Touch Icons:</strong> {missingAppleTouchIcons.length} icônes Apple Touch manquantes pour iOS "Ajouter à l'écran d'accueil". Ces icônes doivent avoir moins de padding que les icônes Android.
                </AlertDescription>
              </Alert>
            )}

            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                onClick={() => window.open('/tools/generate-maskable-icons.html', '_blank')}
              >
                <Download className="h-4 w-4 mr-2" />
                Outil de génération
              </Button>
              <Button
                variant="outline"
                onClick={() => window.open('https://maskable.app/', '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Tester sur Maskable.app
              </Button>
              <Button
                variant="outline"
                onClick={() => window.open('https://www.pwabuilder.com/', '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Valider avec PWA Builder
              </Button>
            </div>
          </div>
        </div>

        {/* Statut final */}
        {!hasIconIssues && icons.length > 0 && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Configuration correcte:</strong> Toutes les icônes sont disponibles et correctement configurées.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
