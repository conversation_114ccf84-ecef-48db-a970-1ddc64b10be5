"use client"

import { useState, useEffect } from "react"
import { X } from "lucide-react"
import { usePWAInstall } from "@/lib/hooks/use-pwa-install"
import { IOSInstallGuide } from "./ios-install-guide"
import { WindowsInstallGuide } from "./windows-install-guide"

export function PWAInstallBanner() {
  const {
    isInstallable,
    isInstalled,
    promptInstall,
    isIOS,
    isWindows,
    showIOSGuide,
    closeIOSGuide,
    capabilities,
    installationDismissed,
    dismissInstallation
  } = usePWAInstall()
  const [showWindowsGuide, setShowWindowsGuide] = useState(false)
  const [browserType, setBrowserType] = useState<"chrome" | "edge" | "firefox" | "other">("chrome")
  const [showBanner, setShowBanner] = useState(false)
  const [dismissed, setDismissed] = useState(false)

  useEffect(() => {
    // Vérifier si la bannière a déjà été fermée dans cette session
    const sessionDismissed = sessionStorage.getItem("pwa-banner-dismissed") === "true"

    if (sessionDismissed || installationDismissed) {
      setDismissed(true)
      return
    }

    // Calculer le délai d'affichage basé sur les capacités
    let delay = 3000 // Délai par défaut

    // Délai plus court pour les navigateurs avec installation native
    if (capabilities.installMethod === "native") {
      delay = 2000
    }
    // Délai plus long pour iOS (installation manuelle)
    else if (capabilities.platform === "ios") {
      delay = 5000
    }

    // Afficher la bannière après le délai si l'app est installable et non installée
    const timer = setTimeout(() => {
      if (isInstallable && !isInstalled && capabilities.supportsPWA) {
        console.log("Affichage de la bannière d'installation", { capabilities })
        setShowBanner(true)
      } else {
        console.log("Conditions non remplies pour afficher la bannière:", {
          isInstallable,
          isInstalled,
          supportsPWA: capabilities.supportsPWA,
          installMethod: capabilities.installMethod
        })
      }
    }, delay)

    return () => clearTimeout(timer)
  }, [isInstallable, isInstalled, capabilities, installationDismissed])

  const handleDismiss = () => {
    setShowBanner(false)
    setDismissed(true)
    sessionStorage.setItem("pwa-banner-dismissed", "true")
  }

  const handlePermanentDismiss = () => {
    dismissInstallation()
    setShowBanner(false)
    setDismissed(true)
    sessionStorage.setItem("pwa-banner-dismissed", "true")
  }

  const handleInstall = () => {
    // Détecter le navigateur pour Windows
    if (capabilities.platform === "windows") {
      setBrowserType(capabilities.browserType)
      setShowWindowsGuide(true)
    }

    promptInstall()
    setShowBanner(false)
  }

  // Générer le message approprié selon les capacités
  const getInstallMessage = () => {
    if (capabilities.platform === "ios") {
      return "Ajoutez ACR Direct à votre écran d'accueil pour un accès rapide"
    } else if (capabilities.installMethod === "native") {
      return "Installez ACR Direct pour une expérience native optimisée"
    } else {
      return "Accédez rapidement à l'application depuis votre écran d'accueil"
    }
  }

  const getInstallButtonText = () => {
    if (capabilities.platform === "ios") {
      return "Voir le guide"
    } else if (capabilities.installMethod === "native") {
      return "Installer"
    } else {
      return "Voir comment faire"
    }
  }

  const closeWindowsGuide = () => {
    setShowWindowsGuide(false)
  }

  if (!showBanner || dismissed || isInstalled) return null

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 z-50 animate-slide-up bg-gradient-to-r from-blue-600 to-blue-700 p-4 shadow-xl">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="mr-4 h-12 w-12 overflow-hidden rounded-xl bg-white p-1 shadow-md">
                <img src="/android-chrome-192x192.png" alt="ACR Direct" className="h-full w-full object-cover rounded-lg" />
              </div>
              <div className="text-white">
                <h3 className="font-bold text-lg">Installer ACR Direct</h3>
                <p className="text-sm text-blue-100 opacity-90">
                  {getInstallMessage()}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={handleInstall}
                className="rounded-lg bg-white px-4 py-2 text-sm font-semibold text-blue-600 shadow-md hover:bg-blue-50 hover:shadow-lg transition-all duration-200"
              >
                {getInstallButtonText()}
              </button>
              <button
                onClick={handleDismiss}
                className="rounded-full p-2 text-white/80 hover:bg-white/20 hover:text-white transition-all duration-200"
                title="Fermer"
              >
                <X size={18} />
              </button>
            </div>
          </div>

          {/* Option "Ne plus afficher" */}
          <div className="mt-3 flex justify-center">
            <button
              onClick={handlePermanentDismiss}
              className="text-xs text-blue-200 hover:text-white underline underline-offset-2 transition-colors duration-200"
            >
              Ne plus me proposer l'installation
            </button>
          </div>
        </div>
      </div>

      {showIOSGuide && <IOSInstallGuide onClose={closeIOSGuide} />}
      {showWindowsGuide && <WindowsInstallGuide onClose={closeWindowsGuide} browserType={browserType} />}
    </>
  )
}
