import type React from "react"
import "./globals.css"
import type { Metadata } from "next"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/components/auth-provider"
import { ThemeSynchronizer } from "@/components/theme-synchronizer"
import { OfflineSyncInitializer } from "@/components/offline-sync-initializer"
import { PWAUpdateHandler } from "@/components/pwa-update-handler"
import { SkipToContent } from "@/components/skip-to-content"
import Script from "next/script"
import { PWAInstallBanner } from "@/components/pwa-install-banner"
import { PWAInstallSuccess } from "@/components/pwa-install-success"
import { OfflineHandler } from "@/components/offline-handler"
import { AutoPrefetch } from "@/components/auto-prefetch"

export const metadata: Metadata = {
  title: "ACR Direct",
  description: "Plateforme B2B pour ACR Direct",
  manifest: "/manifest.json",
  themeColor: "#0d47a1",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "ACR Direct",
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
  },
  generator: "v0.dev",
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <head>
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        {/* Scripts de persistance chargés en premier */}
        <script src="/auth-persistence-helper.js" />
        <script src="/ultra-persistence.js" />
        <script src="/auth-sw-register.js" defer />
      </head>
      <body>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <AuthProvider>
            <SkipToContent />
            {children}
            <ThemeSynchronizer />
            <OfflineSyncInitializer />
            <PWAUpdateHandler />
            <PWAInstallBanner />
            <PWAInstallSuccess />
            <OfflineHandler showAlert={true} redirectToOfflinePage={false} showStatusBadge={true} />
            <AutoPrefetch />
          </AuthProvider>
        </ThemeProvider>
        <Script
          id="register-sw"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('Service Worker registered successfully');

                      // Vérifier les mises à jour toutes les heures
                      setInterval(() => {
                        registration.update()
                          .then(() => console.log('Service Worker update check completed'))
                          .catch(err => console.error('Service Worker update check failed:', err));
                      }, 60 * 60 * 1000);
                    })
                    .catch(function(error) {
                      console.error('Service Worker registration failed:', error);
                    });
                });
              }
            `,
          }}
        />
      </body>
    </html>
  )
}
